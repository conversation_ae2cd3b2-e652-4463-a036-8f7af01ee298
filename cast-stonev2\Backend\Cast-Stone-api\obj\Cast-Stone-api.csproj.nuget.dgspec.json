{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\cast stone final\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\cast stone final\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\cast stone final\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj", "projectName": "Cast-<PERSON>-api", "projectPath": "C:\\Users\\<USER>\\Desktop\\cast stone final\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\cast stone final\\cast-stonev2\\Backend\\Cast-Stone-api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CloudinaryDotNet": {"target": "Package", "version": "[1.27.6, )"}, "MailKit": {"target": "Package", "version": "[4.13.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "PayPalCheckoutSdk": {"target": "Package", "version": "[1.0.4, )"}, "Stripe.net": {"target": "Package", "version": "[48.3.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}