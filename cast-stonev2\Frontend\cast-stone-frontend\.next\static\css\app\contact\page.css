/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/contact/contact.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* Contact Page Styles - Welcome Card Design */
.contact_contactPage__1rDu5 {
  min-height: 100vh;
  background: linear-gradient(135deg, #a5b4fc 0%, #c7d2fe 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin-top: 5rem;
}

.contact_container__cCpH8 {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.contact_cardContainer__eTQwo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact_welcomeCard__bG1Kt {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 1fr 1fr;
  overflow: hidden;
  width: 100%;
  max-width: 1300px;
  min-height: 500px;
}

/* Left Side - Welcome Section */
.contact_leftSection__1GpWc {
  position: relative;
  background: #f8fafc;
  padding: 3rem 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.contact_welcomeContent__ZN25S {
  margin-bottom: 2rem;
}

.contact_formRow__bPMyT {
  display: flex;
  gap: 1rem; /* spacing between the two fields */
}

.contact_formRow__bPMyT .contact_formGroup__p53v_ {
  flex: 1; /* make both fields take equal width */
}


.contact_welcomeTitle__xEfmg {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.contact_welcomeSubtitle__PGb6l {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  font-weight: 400;
}

.contact_welcomeDescription__7CQyG {
  font-size: 0.9rem;
  color: #9ca3af;
  line-height: 1.5;
  margin: 0;
}

.contact_illustrationContainer__63DJy {
  position: absolute;
  bottom: 0;
  left: 2;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* align-items: flex-end; */
  pointer-events: none; 
}

.contact_illustration__hFdvA {
  width: auto;
  max-width: 90%;
  height: 180%;
  border-radius: 8px;
}

/* Right Side - Form Section */
.contact_rightSection__959GB {
  padding: 3rem 2.5rem;
  background: #ffffff;
}

/* Profile Section */
.contact_profileSection__HvxKp {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact_profilePicture__zI93O {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.contact_profileImage__c9i7L {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contact_changeText__ekaER {
  font-size: 0.75rem;
  color: #6366f1;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

/* Form Title */
.contact_formTitle__Oloqn {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.contact_requiredField__Pvrxi {
  color: #9ca3af;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

/* Form Styles */
.contact_contactForm__erSNo {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.contact_formGroup__p53v_ {
  display: flex;
  flex-direction: column;
}

.contact_label__f_aeS {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.contact_input__NXGUw,
.contact_select__hzDyt,
.contact_textarea__nz_oc {
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.95rem;
  color: #374151;
  background: #ffffff;
  transition: all 0.2s ease;
  outline: none;
  width: 100%;
}

.contact_input__NXGUw:focus,
.contact_select__hzDyt:focus,
.contact_textarea__nz_oc:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.contact_input__NXGUw::placeholder,
.contact_textarea__nz_oc::placeholder {
  color: #9ca3af;
}

.contact_select__hzDyt {
  cursor: pointer;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23374151" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  appearance: none;
}

.contact_textarea__nz_oc {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

/* Disclaimer */
.contact_disclaimer__6RtaZ {
  margin: 1rem 0;
}

.contact_disclaimerText__Tv_lx {
  font-size: 0.8rem;
  color: #9ca3af;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.contact_disclaimerText__Tv_lx:last-child {
  margin-bottom: 0;
}

/* Submit Button */
.contact_submitButton__Pottv {
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 0.875rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.contact_submitButton__Pottv:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

.contact_submitButton__Pottv:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.contact_submitting__6tiZL {
  background: #9ca3af !important;
}

/* Submit Message */
.contact_submitMessage__xZqfP {
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.contact_submitMessage__xZqfP.contact_success__UwdEQ {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.contact_submitMessage__xZqfP.contact_error__fEMTu {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact_contactPage__1rDu5 {
    padding: 1rem;
  }
  .contact_illustration__hFdvA{
    display: none;
  }

  .contact_formRow__bPMyT {
    flex-direction: column;
  }
  .contact_welcomeCard__bG1Kt {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .contact_leftSection__1GpWc {
    padding: 2rem 1.5rem;
    order: 1;
  }

  .contact_rightSection__959GB {
    padding: 2rem 1.5rem;
    order: 2;
  }

  .contact_welcomeTitle__xEfmg {
    font-size: 1.5rem;
  }

  .contact_formTitle__Oloqn {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .contact_contactPage__1rDu5 {
    padding: 0.5rem;
  }

  .contact_leftSection__1GpWc,
  .contact_rightSection__959GB {
    padding: 1.5rem 1rem;
  }

  .contact_welcomeTitle__xEfmg {
    font-size: 1.25rem;
  }

  .contact_welcomeSubtitle__PGb6l {
    font-size: 1rem;
  }

  .contact_welcomeDescription__7CQyG {
    font-size: 0.85rem;
  }

  .contact_illustration__hFdvA {
    max-width: 200px;
  }

  .contact_profileSection__HvxKp {
    margin-bottom: 1.5rem;
  }

  .contact_formTitle__Oloqn {
    font-size: 1.1rem;
  }

  .contact_contactForm__erSNo {
    gap: 1rem;
  }

  .contact_input__NXGUw,
  .contact_select__hzDyt,
  .contact_textarea__nz_oc {
    padding: 0.625rem 0.875rem;
    font-size: 0.9rem;
  }

  .contact_submitButton__Pottv {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }
}

