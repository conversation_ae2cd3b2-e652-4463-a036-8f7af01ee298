.featuresSection {
  @apply py-16 bg-white;
}

.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.title {
  @apply text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12;
}

.featuresGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

.featureCard {
  @apply bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow duration-300;
}

.icon {
  @apply text-4xl mb-4;
}

.featureTitle {
  @apply text-xl font-semibold text-gray-900 mb-3;
}

.featureDescription {
  @apply text-gray-600 leading-relaxed;
}
