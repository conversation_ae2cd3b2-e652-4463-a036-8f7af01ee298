/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect, useRef } from 'react';
import styles from './contact.module.css';
import { contactFormPostService } from '@/services/api/contactForm/post';
import { InquiryType } from '@/services/types/entities';

interface FormData {
  name: string;
  email: string;
  phoneNumber: string;
  company: string;
  state: string;
  inquiry: InquiryType | '';
  message: string;
}

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phoneNumber: '',
    company: '',
    state: '',
    inquiry: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const contactRef = useRef<HTMLDivElement>(null);

  const inquiryOptions = [
    { value: InquiryType.ProductInquiry, label: 'Product Inquiry' },
    { value: InquiryType.RequestDesignConsultation, label: 'Request a Design Consultation' },
    { value: InquiryType.CustomOrders, label: 'Custom Orders' },
    { value: InquiryType.TradePartnerships, label: 'Trade Partnerships' },
    { value: InquiryType.InstallationSupport, label: 'Installation Support' },
    { value: InquiryType.ShippingAndLeadTimes, label: 'Shipping & Lead Times' },
    { value: InquiryType.RequestCatalogPriceList, label: 'Request a Catalog / Price List' },
    { value: InquiryType.MediaPressInquiry, label: 'Media / Press Inquiry' },
    { value: InquiryType.GeneralQuestions, label: 'General Questions' }
  ];

  const stateOptions = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware',
    'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky',
    'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi',
    'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico',
    'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania',
    'Rhode Island', 'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont',
    'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
  ];

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'inquiry' ? (value === '' ? '' : parseInt(value) as InquiryType) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage(null);

    try {
      if (formData.inquiry === '') {
        throw new Error('Please select an inquiry type');
      }

      await contactFormPostService.submit({
        name: formData.name,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        company: formData.company || undefined,
        state: formData.state,
        inquiry: formData.inquiry,
        message: formData.message
      });

      // Reset form on success
      setFormData({
        name: '',
        email: '',
        phoneNumber: '',
        company: '',
        state: '',
        inquiry: '',
        message: ''
      });

      setSubmitMessage({
        type: 'success',
        text: 'Thank you for your message! We will get back to you soon.'
      });

    } catch (error) {
      setSubmitMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'An error occurred. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.contactPage} ref={contactRef}>
      <div className={styles.container}>
        {/* Hero Section */}
        <div className={`${styles.heroSection} ${isVisible ? styles.visible : ''}`}>
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>
              <span className={styles.animatedText}>Get in touch</span>
              <span className={styles.animatedText}>with us</span>
            </h1>
            <p className={styles.heroSubtitle}>
              <span className={styles.animatedText}>We are ready to lead you into the exciting world</span>
              <span className={styles.animatedText}>of cast stone architecture and design.</span>
            </p>
          </div>
        </div>

        {/* Contact Information Grid */}
        <div className={`${styles.contactGrid} ${isVisible ? styles.visible : ''}`}>
          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>Project Inquiries</h3>
            <a href="mailto:<EMAIL>" className={styles.contactLink}>
              <EMAIL>
            </a>
          </div>

          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>Design Consultations</h3>
            <a href="mailto:<EMAIL>" className={styles.contactLink}>
              <EMAIL>
            </a>
          </div>

          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>General Questions</h3>
            <a href="mailto:<EMAIL>" className={styles.contactLink}>
              <EMAIL>
            </a>
          </div>

          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>Phone</h3>
            <a href="tel:+1234567890" className={styles.contactLink}>
              +1 (234) 567-890
            </a>
          </div>

          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>Location</h3>
            <div className={styles.addressText}>
              123 Stone Avenue<br />
              Design District<br />
              New York, NY 10001
            </div>
          </div>
        </div>

        {/* Contact Form Section */}
        <div className={`${styles.formSection} ${isVisible ? styles.visible : ''}`}>
          <div className={styles.formContainer}>
            <div className={styles.formHeader}>
              <h2 className={styles.formTitle}>Send us a message</h2>
              <p className={styles.formSubtitle}>*Required fields</p>
            </div>

            {submitMessage && (
              <div className={`${styles.submitMessage} ${styles[submitMessage.type]}`}>
                {submitMessage.text}
              </div>
            )}

            <form onSubmit={handleSubmit} className={styles.contactForm}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="name" className={styles.label}>*Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={styles.input}
                    required
                    placeholder="Your full name"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="email" className={styles.label}>*Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={styles.input}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="phoneNumber" className={styles.label}>*Phone Number</label>
                  <input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className={styles.input}
                    required
                    placeholder="+1 (234) 567-890"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="company" className={styles.label}>Company</label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    className={styles.input}
                    placeholder="Your company name"
                  />
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="state" className={styles.label}>*State</label>
                  <select
                    id="state"
                    name="state"
                    value={formData.state}
                    onChange={handleInputChange}
                    className={styles.select}
                    required
                  >
                    <option value="">Select your state...</option>
                    {stateOptions.map((state) => (
                      <option key={state} value={state}>
                        {state}
                      </option>
                    ))}
                  </select>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="inquiry" className={styles.label}>*Inquiry Type</label>
                  <select
                    id="inquiry"
                    name="inquiry"
                    value={formData.inquiry}
                    onChange={handleInputChange}
                    className={styles.select}
                    required
                  >
                    <option value="">Select inquiry type...</option>
                    {inquiryOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="message" className={styles.label}>*Message</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className={styles.textarea}
                  rows={5}
                  placeholder="Tell us about your project or inquiry..."
                  required
                  minLength={10}
                  maxLength={2000}
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`${styles.submitButton} ${isSubmitting ? styles.submitting : ''}`}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
