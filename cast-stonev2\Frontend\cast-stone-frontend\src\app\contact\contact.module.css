/* Contact Page Styles - Welcome Card Design */
.contactPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #a5b4fc 0%, #c7d2fe 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin-top: 5rem;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.cardContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.welcomeCard {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 1fr 1fr;
  overflow: hidden;
  width: 100%;
  max-width: 1300px;
  min-height: 500px;
}

/* Left Side - Welcome Section */
.leftSection {
  position: relative;
  background: #f8fafc;
  padding: 3rem 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.welcomeContent {
  margin-bottom: 2rem;
}

.formRow {
  display: flex;
  gap: 1rem; /* spacing between the two fields */
}

.formRow .formGroup {
  flex: 1; /* make both fields take equal width */
}


.welcomeTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.welcomeSubtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  font-weight: 400;
}

.welcomeDescription {
  font-size: 0.9rem;
  color: #9ca3af;
  line-height: 1.5;
  margin: 0;
}

.illustrationContainer {
  position: absolute;
  bottom: 0;
  left: 2;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* align-items: flex-end; */
  pointer-events: none; 
}

.illustration {
  width: auto;
  max-width: 90%;
  height: 180%;
  border-radius: 8px;
}

/* Right Side - Form Section */
.rightSection {
  padding: 3rem 2.5rem;
  background: #ffffff;
}

/* Profile Section */
.profileSection {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.profilePicture {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.profileImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.changeText {
  font-size: 0.75rem;
  color: #6366f1;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

/* Form Title */
.formTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.requiredField {
  color: #9ca3af;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

/* Form Styles */
.contactForm {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.input,
.select,
.textarea {
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.95rem;
  color: #374151;
  background: #ffffff;
  transition: all 0.2s ease;
  outline: none;
  width: 100%;
}

.input:focus,
.select:focus,
.textarea:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input::placeholder,
.textarea::placeholder {
  color: #9ca3af;
}

.select {
  cursor: pointer;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23374151" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  appearance: none;
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

/* Disclaimer */
.disclaimer {
  margin: 1rem 0;
}

.disclaimerText {
  font-size: 0.8rem;
  color: #9ca3af;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.disclaimerText:last-child {
  margin-bottom: 0;
}

/* Submit Button */
.submitButton {
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 0.875rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submitting {
  background: #9ca3af !important;
}

/* Submit Message */
.submitMessage {
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.submitMessage.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.submitMessage.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contactPage {
    padding: 1rem;
  }
  .illustration{
    display: none;
  }

  .formRow {
    flex-direction: column;
  }
  .welcomeCard {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .leftSection {
    padding: 2rem 1.5rem;
    order: 1;
  }

  .rightSection {
    padding: 2rem 1.5rem;
    order: 2;
  }

  .welcomeTitle {
    font-size: 1.5rem;
  }

  .formTitle {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .contactPage {
    padding: 0.5rem;
  }

  .leftSection,
  .rightSection {
    padding: 1.5rem 1rem;
  }

  .welcomeTitle {
    font-size: 1.25rem;
  }

  .welcomeSubtitle {
    font-size: 1rem;
  }

  .welcomeDescription {
    font-size: 0.85rem;
  }

  .illustration {
    max-width: 200px;
  }

  .profileSection {
    margin-bottom: 1.5rem;
  }

  .formTitle {
    font-size: 1.1rem;
  }

  .contactForm {
    gap: 1rem;
  }

  .input,
  .select,
  .textarea {
    padding: 0.625rem 0.875rem;
    font-size: 0.9rem;
  }

  .submitButton {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }
}
