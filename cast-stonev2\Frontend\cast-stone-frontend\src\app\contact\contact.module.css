/* Contact Page Styles - Modern Design with Animations */
.contactPage {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #ffffff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: rgba(37, 99, 235, 0.1);
  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  min-height: 100vh;
  background: var(--cast-stone-white);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding-top: 6rem;
  overflow-x: hidden;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  text-align: center;
  padding: 4rem 0 6rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.heroSection.visible {
  opacity: 1;
  transform: translateY(0);
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 300;
  color: var(--cast-stone-dark-text);
  margin-bottom: 2rem;
  line-height: 1.2;
  overflow: hidden;
}

.heroSubtitle {
  font-size: clamp(1.1rem, 2vw, 1.3rem);
  color: var(--cast-stone-gray-text);
  line-height: 1.6;
  margin-bottom: 2rem;
  overflow: hidden;
}

.animatedText {
  display: block;
  opacity: 0;
  transform: translateY(100%);
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.heroSection.visible .animatedText:nth-child(1) {
  animation-delay: 0.2s;
}

.heroSection.visible .animatedText:nth-child(2) {
  animation-delay: 0.4s;
}

.heroSubtitle .animatedText:nth-child(1) {
  animation-delay: 0.6s;
}

.heroSubtitle .animatedText:nth-child(2) {
  animation-delay: 0.8s;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Contact Grid */
.contactGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
  margin-bottom: 6rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s;
}

.contactGrid.visible {
  opacity: 1;
  transform: translateY(0);
}

.contactSection {
  text-align: left;
  padding: 2rem 0;
  border-bottom: 1px solid #e5e7eb;
  transition: var(--transition-smooth);
}

.contactSection:hover {
  transform: translateY(-2px);
}

.sectionTitle {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--cast-stone-gray-text);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
}

.contactLink {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--cast-stone-dark-text);
  text-decoration: none;
  transition: var(--transition-smooth);
  display: block;
}

.contactLink:hover {
  color: var(--cast-stone-blue);
  transform: translateX(5px);
}

.addressText {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--cast-stone-dark-text);
  line-height: 1.4;
}

/* Form Section */
.formSection {
  background: var(--cast-stone-blue-50);
  border-radius: 16px;
  padding: 4rem;
  margin-bottom: 4rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
}

.formSection.visible {
  opacity: 1;
  transform: translateY(0);
}

.formContainer {
  max-width: 800px;
  margin: 0 auto;
}

.formHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.formTitle {
  font-size: 2rem;
  font-weight: 300;
  color: var(--cast-stone-dark-text);
  margin-bottom: 0.5rem;
}

.formSubtitle {
  color: var(--cast-stone-gray-text);
  font-size: 0.9rem;
}

/* Form Styles */
.contactForm {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--cast-stone-dark-text);
  margin-bottom: 0.5rem;
}

.input,
.select,
.textarea {
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  color: var(--cast-stone-dark-text);
  background: var(--cast-stone-white);
  transition: var(--transition-fast);
  outline: none;
  width: 100%;
}

.input:focus,
.select:focus,
.textarea:focus {
  border-color: var(--cast-stone-blue);
  box-shadow: 0 0 0 3px var(--cast-stone-shadow);
}

.input::placeholder,
.textarea::placeholder {
  color: #9ca3af;
}

.select {
  cursor: pointer;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%234b5563" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px;
  appearance: none;
}

.textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

/* Submit Button */
.submitButton {
  background: var(--cast-stone-blue);
  color: var(--cast-stone-white);
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-top: 1rem;
  align-self: flex-start;
}

.submitButton:hover:not(:disabled) {
  background: var(--cast-stone-light-blue);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--cast-stone-shadow-hover);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submitButton.submitting {
  background: #9ca3af;
}

/* Submit Message */
.submitMessage {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.submitMessage.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.submitMessage.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .heroSection {
    padding: 2rem 0 4rem;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .contactGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .formSection {
    padding: 2rem;
    border-radius: 12px;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .contactForm {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contactPage {
    padding-top: 5rem;
  }

  .container {
    padding: 0 0.5rem;
  }

  .heroSection {
    padding: 1.5rem 0 3rem;
  }

  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1rem;
  }

  .contactGrid {
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .contactSection {
    padding: 1.5rem 0;
  }

  .sectionTitle {
    font-size: 0.8rem;
  }

  .contactLink,
  .addressText {
    font-size: 1.2rem;
  }

  .formSection {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .formHeader {
    margin-bottom: 2rem;
  }

  .formTitle {
    font-size: 1.3rem;
  }

  .input,
  .select,
  .textarea {
    padding: 0.875rem;
    font-size: 0.95rem;
  }

  .submitButton {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
    align-self: stretch;
  }
}
